<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-15 16:29:02
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-08-01 15:43:14
 * @FilePath: /mb-users-magic-brush/src/components/ChatAiTool/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai-tool" @click="close" v-if="filteredTools.length">
    <div class="content" @click.stop>
      <!-- 顶部渐变遮罩 -->
      <div class="scroll-gradient scroll-gradient-top" v-show="showTopGradient"></div>

      <div class="tools-container" @scroll="handleScroll" ref="toolsContainer">
        <div class="tools">
          <div @click="chooseTool(item.functionName)" class="item" v-for="item in filteredTools"
            :key="item.functionName">
            <span>{{ item.functionName }}</span>
            <span class="tips">&emsp;{{ item.tips }}</span>
          </div>
        </div>
      </div>

      <!-- 底部渐变遮罩 -->
      <div class="scroll-gradient scroll-gradient-bottom" v-show="showBottomGradient"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useChatStore from '../../store/modules/chat'
import { ref, computed, onMounted, nextTick } from 'vue'

const chatStore = useChatStore()

defineOptions({
  name: 'ChatAiTool',
})

// 滚动相关的响应式数据
const toolsContainer = ref<HTMLElement>()
const showTopGradient = ref(false)
const showBottomGradient = ref(false)

const chooseTool = (tool: string) => {
  chatStore.tool = tool
  close()
}

const close = () => {
  chatStore.setShowAiTool(false)
}

const filteredTools = computed(() => {
  const tools = chatStore.tools?.flatMap(item => item.toolList)
  return tools.filter(item => item.functionName.includes(chatStore.filterKeyword))
})

// 处理滚动事件，控制渐变遮罩的显示
const handleScroll = () => {
  if (!toolsContainer.value) return

  const { scrollTop, scrollHeight, clientHeight } = toolsContainer.value

  // 顶部渐变：当向下滚动时显示
  showTopGradient.value = scrollTop > 10

  // 底部渐变：当还能继续向下滚动时显示
  showBottomGradient.value = scrollTop < scrollHeight - clientHeight - 10
}

// 检查是否需要显示滚动渐变
const checkScrollGradients = () => {
  nextTick(() => {
    if (!toolsContainer.value) return

    const { scrollHeight, clientHeight } = toolsContainer.value

    // 如果内容高度超过容器高度，显示底部渐变
    if (scrollHeight > clientHeight) {
      showBottomGradient.value = true
    }

    // 初始化时不显示顶部渐变
    showTopGradient.value = false
  })
}

// 监听工具列表变化，重新检查滚动状态
onMounted(() => {
  checkScrollGradients()
})

// 当工具列表发生变化时，重新检查滚动状态
computed(() => {
  checkScrollGradients()
  return filteredTools.value
})
</script>

<style lang="scss" scoped>
.ai-tool {
  position: absolute;
  bottom: 120%;
  left: 0;
  width: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .content {
    position: relative;
    width: 100%;
    background: #ffffff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    border-radius: 15px;
    max-height: 400px; // 限制最大高度，确保能够滚动
    overflow: hidden; // 隐藏溢出，由内部容器处理滚动

    // 滚动容器
    .tools-container {
      height: 100%;
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0; // 移除默认内边距

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      // 平滑滚动
      scroll-behavior: smooth;
    }

    // 渐变遮罩样式
    .scroll-gradient {
      position: absolute;
      left: 0;
      right: 0;
      height: 20px;
      pointer-events: none; // 不阻挡点击事件
      z-index: 10;

      &.scroll-gradient-top {
        top: 0;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 15px 15px 0 0;
      }

      &.scroll-gradient-bottom {
        bottom: 0;
        background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0 0 15px 15px;
      }
    }

    .tools {
      margin: 0 25px;
      padding-top: 25px;
      padding-bottom: 25px; // 确保底部有足够间距

      .item {
        height: 40px;
        margin-bottom: 10px;
        font-size: 18px;
        color: #2266ff;
        cursor: pointer; // 添加鼠标指针样式
        transition: all 0.2s ease; // 添加过渡动画

        &:hover {
          background-color: rgba(34, 102, 255, 0.05); // 悬停背景色
          border-radius: 8px;
          padding: 0 8px;
          margin: 0 -8px 10px -8px;
        }

        &:last-child {
          margin-bottom: 0; // 最后一项不需要底部间距
        }

        img {
          height: 22px;
          margin-right: 17px;
        }

        .title {
          font-weight: normal;
          font-size: 20px;
          color: #000000;
        }

        .tips {
          color: #909090;
        }
      }
    }
  }
}
</style>
